/**
 * 格式化數字為千分位顯示
 * @param num 數字
 * @param decimals 小數位數
 * @returns 格式化後的字串
 */
export function formatNumber(num: number, decimals: number = 0): string {
  return new Intl.NumberFormat('zh-TW', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(num);
}

/**
 * 格式化貨幣顯示
 * @param amount 金額
 * @param currency 貨幣代碼
 * @returns 格式化後的字串
 */
export function formatCurrency(amount: number, currency: string = 'TWD'): string {
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

/**
 * 格式化百分比顯示
 * @param rate 比率 (小數)
 * @param decimals 小數位數
 * @returns 格式化後的字串
 */
export function formatPercentage(rate: number, decimals: number = 2): string {
  return new Intl.NumberFormat('zh-TW', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(rate / 100);
}

/**
 * 解析數字字串，移除千分位符號
 * @param str 數字字串
 * @returns 數字
 */
export function parseNumber(str: string): number {
  if (!str) return 0;
  // 移除千分位符號和空格
  const cleaned = str.replace(/[,\s]/g, '');
  const num = parseFloat(cleaned);
  return isNaN(num) ? 0 : num;
}

/**
 * 驗證是否為有效數字
 * @param value 值
 * @param min 最小值
 * @param max 最大值
 * @returns 是否有效
 */
export function isValidNumber(value: any, min?: number, max?: number): boolean {
  const num = typeof value === 'string' ? parseNumber(value) : value;

  if (typeof num !== 'number' || isNaN(num)) {
    return false;
  }

  if (min !== undefined && num < min) {
    return false;
  }

  if (max !== undefined && num > max) {
    return false;
  }

  return true;
}

/**
 * 四捨五入到指定小數位數
 * @param num 數字
 * @param decimals 小數位數
 * @returns 四捨五入後的數字
 */
export function roundTo(num: number, decimals: number): number {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

/**
 * 計算兩個數字的差異百分比
 * @param oldValue 舊值
 * @param newValue 新值
 * @returns 差異百分比
 */
export function calculatePercentageChange(oldValue: number, newValue: number): number {
  if (oldValue === 0) return newValue === 0 ? 0 : 100;
  return ((newValue - oldValue) / oldValue) * 100;
}

/**
 * 深度複製物件
 * @param obj 要複製的物件
 * @returns 複製後的物件
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }

  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }

  return obj;
}

/**
 * 防抖函數
 * @param func 要執行的函數
 * @param delay 延遲時間 (毫秒)
 * @returns 防抖後的函數
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * 節流函數
 * @param func 要執行的函數
 * @param delay 延遲時間 (毫秒)
 * @returns 節流後的函數
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;

  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}
